import pyautogui
import pandas as pd
import time
from datetime import datetime

def capture_and_save_basic():
    """基础版本：截图并保存基本信息到Excel"""
    print("=== 基础表单信息捕获 ===")
    
    # 获取屏幕信息
    screen_width, screen_height = pyautogui.size()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 获取当前鼠标位置
    mouse_x, mouse_y = pyautogui.position()
    
    # 截取屏幕
    print("正在截取屏幕...")
    screenshot = pyautogui.screenshot()
    screenshot_filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    screenshot.save(screenshot_filename)
    
    # 创建基本数据
    data = {
        '捕获时间': [current_time],
        '屏幕分辨率': [f"{screen_width}x{screen_height}"],
        '鼠标位置': [f"({mouse_x}, {mouse_y})"],
        '截图文件': [screenshot_filename],
        '备注': ['基础屏幕捕获']
    }
    
    # 保存到Excel
    df = pd.DataFrame(data)
    excel_filename = f"screen_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"截图已保存: {screenshot_filename}")
    print(f"数据已保存到Excel: {excel_filename}")
    
    return data

def capture_multiple_regions():
    """捕获多个区域并保存到Excel"""
    print("=== 多区域捕获模式 ===")
    print("这个模式将让你选择多个区域进行捕获")
    
    regions_data = []
    region_count = 1
    
    while True:
        print(f"\n--- 区域 {region_count} ---")
        print("请将鼠标移动到区域左上角，按回车继续...")
        input()
        
        start_x, start_y = pyautogui.position()
        print(f"左上角位置: ({start_x}, {start_y})")
        
        print("请将鼠标移动到区域右下角，按回车继续...")
        input()
        
        end_x, end_y = pyautogui.position()
        print(f"右下角位置: ({end_x}, {end_y})")
        
        # 计算区域
        width = end_x - start_x
        height = end_y - start_y
        
        if width > 0 and height > 0:
            # 截取区域
            region_image = pyautogui.screenshot(region=(start_x, start_y, width, height))
            region_filename = f"region_{region_count}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            region_image.save(region_filename)
            
            # 记录数据
            region_data = {
                '区域编号': region_count,
                '捕获时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                '左上角坐标': f"({start_x}, {start_y})",
                '右下角坐标': f"({end_x}, {end_y})",
                '区域大小': f"{width}x{height}",
                '截图文件': region_filename
            }
            
            regions_data.append(region_data)
            print(f"区域 {region_count} 已捕获: {region_filename}")
            
            region_count += 1
        else:
            print("无效区域，跳过")
        
        # 询问是否继续
        continue_choice = input("\n是否继续捕获下一个区域？(y/n): ").lower()
        if continue_choice != 'y':
            break
    
    # 保存所有区域数据到Excel
    if regions_data:
        df = pd.DataFrame(regions_data)
        excel_filename = f"regions_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        print(f"\n所有区域数据已保存到: {excel_filename}")
    
    return regions_data

def get_pixel_colors_grid():
    """获取屏幕网格像素颜色信息"""
    print("=== 像素颜色网格分析 ===")
    
    screen_width, screen_height = pyautogui.size()
    
    # 创建网格采样点
    grid_size = 10  # 10x10 网格
    step_x = screen_width // grid_size
    step_y = screen_height // grid_size
    
    color_data = []
    
    print("正在采样屏幕颜色...")
    for i in range(grid_size):
        for j in range(grid_size):
            x = i * step_x + step_x // 2
            y = j * step_y + step_y // 2
            
            # 获取像素颜色
            r, g, b = pyautogui.pixel(x, y)
            
            color_data.append({
                '网格X': i,
                '网格Y': j,
                '屏幕X': x,
                '屏幕Y': y,
                'R值': r,
                'G值': g,
                'B值': b,
                'RGB': f"({r},{g},{b})"
            })
    
    # 保存颜色数据
    df = pd.DataFrame(color_data)
    excel_filename = f"color_grid_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"颜色网格数据已保存到: {excel_filename}")
    return color_data

if __name__ == "__main__":
    print("=== PyAutoGUI 屏幕捕获工具 ===")
    print("选择功能:")
    print("1. 基础屏幕捕获")
    print("2. 多区域捕获")
    print("3. 像素颜色网格分析")
    
    try:
        choice = input("请输入选择 (1/2/3): ")
        
        if choice == "1":
            capture_and_save_basic()
        elif choice == "2":
            capture_multiple_regions()
        elif choice == "3":
            get_pixel_colors_grid()
        else:
            print("无效选择，运行基础捕获")
            capture_and_save_basic()
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        print("请确保已安装所有依赖包")
