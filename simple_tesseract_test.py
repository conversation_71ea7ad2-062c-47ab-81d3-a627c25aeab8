import pyautogui
import pandas as pd
import os
import time
from datetime import datetime

# 检查pytesseract
try:
    import pytesseract
    print("✅ pytesseract 已安装")
    
    # 设置Tesseract路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe"
    ]
    
    tesseract_found = False
    for path in possible_paths:
        if os.path.exists(path):
            pytesseract.pytesseract.tesseract_cmd = path
            print(f"✅ 找到Tesseract: {path}")
            tesseract_found = True
            break
    
    if not tesseract_found:
        print("⚠ 尝试使用系统PATH中的tesseract")
        pytesseract.pytesseract.tesseract_cmd = "tesseract"
    
    # 测试Tesseract版本
    try:
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract版本: {version}")
        TESSERACT_READY = True
    except Exception as e:
        print(f"❌ Tesseract测试失败: {e}")
        TESSERACT_READY = False
        
except ImportError:
    print("❌ pytesseract 未安装")
    TESSERACT_READY = False

def test_tesseract_ocr():
    """测试Tesseract OCR功能"""
    if not TESSERACT_READY:
        print("Tesseract不可用，跳过测试")
        return
    
    print("\n=== Tesseract 屏幕文字识别测试 ===")
    print("请确保屏幕上有要识别的文字")
    print("5秒后开始截图...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截图
    print("\n📸 正在截图...")
    screenshot = pyautogui.screenshot()
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"tesseract_screenshot_{timestamp}.png"
    screenshot.save(screenshot_filename)
    print(f"截图保存: {screenshot_filename}")
    
    # 开始OCR
    print("🔍 正在使用Tesseract识别文字...")
    start_time = time.time()
    
    try:
        # 基础文字识别
        text = pytesseract.image_to_string(screenshot, lang='chi_sim+eng')
        
        # 获取详细数据
        data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱ 识别完成，用时: {processing_time:.2f} 秒")
        
        # 处理结果
        valid_results = []
        for i in range(len(data['text'])):
            confidence = int(data['conf'][i])
            text_item = data['text'][i].strip()
            
            if confidence > 30 and text_item:
                valid_results.append({
                    '文字': text_item,
                    '置信度': confidence,
                    'X坐标': data['left'][i],
                    'Y坐标': data['top'][i],
                    '宽度': data['width'][i],
                    '高度': data['height'][i]
                })
        
        # 显示结果
        print(f"\n📊 识别统计:")
        print(f"   - 总文字长度: {len(text)} 字符")
        print(f"   - 有效文字块: {len(valid_results)} 个")
        print(f"   - 处理时间: {processing_time:.2f} 秒")
        
        print(f"\n📝 识别的文字内容:")
        print("-" * 60)
        print(text[:500] + "..." if len(text) > 500 else text)
        print("-" * 60)
        
        # 保存到Excel
        if valid_results:
            df = pd.DataFrame(valid_results)
            df['截图文件'] = screenshot_filename
            df['识别时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            df['处理时间(秒)'] = processing_time
            
            excel_filename = f"Tesseract结果_{timestamp}.xlsx"
            df.to_excel(excel_filename, index=False, engine='openpyxl')
            print(f"✅ 结果已保存到Excel: {excel_filename}")
        
        # 保存完整文字
        if text.strip():
            text_filename = f"Tesseract文字_{timestamp}.txt"
            with open(text_filename, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"✅ 完整文字已保存: {text_filename}")
        
        return {
            'engine': 'Tesseract',
            'processing_time': processing_time,
            'text_count': len(valid_results),
            'full_text': text,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ Tesseract识别失败: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🔍 Tesseract OCR 测试程序")
    
    if TESSERACT_READY:
        test_tesseract_ocr()
    else:
        print("\n❌ Tesseract不可用")
        print("请确保:")
        print("1. 已安装 pytesseract: pip install pytesseract")
        print("2. 已安装 Tesseract OCR 引擎")
        print("3. Tesseract安装在标准路径或已添加到PATH")
