import pyautogui
import pandas as pd
import time
from datetime import datetime
import base64
import io
import re

# 方案1: 使用easyocr (无需安装Tesseract)
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

# 方案2: 使用pytesseract (需要Tesseract)
try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
    # 尝试常见的Tesseract路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            pytesseract.pytesseract.tesseract_cmd = path
            break
except ImportError:
    PYTESSERACT_AVAILABLE = False

def install_easyocr():
    """安装EasyOCR作为替代方案"""
    print("正在安装 EasyOCR...")
    try:
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "easyocr"])
        print("✓ EasyOCR 安装成功")
        return True
    except Exception as e:
        print(f"✗ EasyOCR 安装失败: {e}")
        return False

def extract_text_easyocr(image):
    """使用EasyOCR提取文字"""
    try:
        # 初始化EasyOCR读取器（支持中英文）
        reader = easyocr.Reader(['ch_sim', 'en'])
        
        # 将PIL图像转换为numpy数组
        import numpy as np
        image_array = np.array(image)
        
        # 进行OCR识别
        results = reader.readtext(image_array)
        
        # 提取文字
        text_lines = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # 只保留置信度较高的结果
                text_lines.append(text)
        
        return '\n'.join(text_lines)
    except Exception as e:
        print(f"EasyOCR识别出错: {e}")
        return ""

def extract_text_pytesseract(image):
    """使用Pytesseract提取文字"""
    try:
        # 使用Tesseract进行OCR
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        return text.strip()
    except Exception as e:
        print(f"Pytesseract识别出错: {e}")
        return ""

def extract_text_from_image(image):
    """智能选择OCR方法提取文字"""
    text = ""
    
    if EASYOCR_AVAILABLE:
        print("使用 EasyOCR 进行文字识别...")
        text = extract_text_easyocr(image)
    elif PYTESSERACT_AVAILABLE:
        print("使用 Pytesseract 进行文字识别...")
        text = extract_text_pytesseract(image)
    else:
        print("⚠ 没有可用的OCR引擎")
        print("请运行以下命令安装EasyOCR:")
        print("pip install easyocr")
        return "OCR引擎不可用"
    
    return text

def parse_form_fields(text):
    """解析表单字段"""
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    form_data = {}
    
    # 常见的表单字段模式
    patterns = [
        r'(.+?)[：:]\s*(.+)',  # 键值对模式
        r'(.+?)\s+(.+)',       # 空格分隔模式
    ]
    
    for line in lines:
        for pattern in patterns:
            match = re.match(pattern, line)
            if match:
                key = match.group(1).strip()
                value = match.group(2).strip()
                
                # 过滤掉太短或无意义的键值对
                if len(key) > 1 and len(value) > 0 and len(key) < 50:
                    form_data[key] = value
                break
    
    return form_data

def capture_form_to_excel():
    """完整的表单捕获到Excel流程"""
    print("=== 表单识别并写入Excel ===")
    
    # 检查OCR可用性
    if not EASYOCR_AVAILABLE and not PYTESSERACT_AVAILABLE:
        print("正在安装 EasyOCR...")
        if install_easyocr():
            global EASYOCR_AVAILABLE
            EASYOCR_AVAILABLE = True
            import easyocr
        else:
            print("无法安装OCR引擎，程序退出")
            return
    
    print("\\n请确保表单在屏幕上可见...")
    print("5秒后开始截图...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截取屏幕
    print("正在截取屏幕...")
    screenshot = pyautogui.screenshot()
    
    # 保存截图
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"form_screenshot_{timestamp}.png"
    screenshot.save(screenshot_filename)
    print(f"截图已保存: {screenshot_filename}")
    
    # 进行OCR识别
    print("正在识别文字...")
    extracted_text = extract_text_from_image(screenshot)
    
    print("\\n识别结果:")
    print("-" * 60)
    print(extracted_text)
    print("-" * 60)
    
    # 解析表单字段
    form_data = parse_form_fields(extracted_text)
    
    print("\\n解析的表单字段:")
    if form_data:
        for key, value in form_data.items():
            print(f"{key}: {value}")
    else:
        print("未识别到明确的表单字段")
    
    # 准备Excel数据
    excel_data = []
    
    if form_data:
        # 如果有结构化数据，保存为键值对
        for key, value in form_data.items():
            excel_data.append({
                '字段名': key,
                '字段值': value,
                '识别时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                '截图文件': screenshot_filename
            })
    else:
        # 如果没有结构化数据，保存原始文字
        excel_data.append({
            '字段名': '原始识别文字',
            '字段值': extracted_text,
            '识别时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            '截图文件': screenshot_filename
        })
    
    # 保存到Excel
    df = pd.DataFrame(excel_data)
    excel_filename = f"form_data_{timestamp}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"\\n✓ 表单数据已保存到Excel: {excel_filename}")
    print(f"✓ 共识别 {len(excel_data)} 个字段")
    
    return excel_data

if __name__ == "__main__":
    try:
        capture_form_to_excel()
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
