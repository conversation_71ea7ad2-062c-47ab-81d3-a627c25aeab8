import pyautogui
import easyocr
import pandas as pd
import numpy as np
import time
from datetime import datetime

def test_easyocr_screen():
    """测试EasyOCR识别屏幕文字功能"""
    print("=== EasyOCR 屏幕文字识别测试 ===")
    
    try:
        # 初始化EasyOCR读取器
        print("正在初始化EasyOCR读取器...")
        print("支持语言: 中文简体 + 英文")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 使用CPU模式，更稳定
        print("✓ EasyOCR 初始化成功")
        
        # 等待用户准备
        print("\n请确保屏幕上有要识别的文字内容")
        print("建议打开一个包含文字的网页、文档或应用程序")
        print("5秒后开始截图和识别...")
        
        for i in range(5, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # 截取屏幕
        print("\n正在截取屏幕...")
        screenshot = pyautogui.screenshot()
        
        # 保存截图
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        screenshot_filename = f"ocr_test_screenshot_{timestamp}.png"
        screenshot.save(screenshot_filename)
        print(f"截图已保存: {screenshot_filename}")
        
        # 转换为numpy数组供EasyOCR使用
        print("正在进行文字识别...")
        image_array = np.array(screenshot)
        
        # 进行OCR识别
        results = reader.readtext(image_array)
        
        print(f"\n🎉 识别完成！共找到 {len(results)} 个文字区域")
        
        # 处理识别结果
        ocr_data = []
        all_text = []
        
        print("\n" + "="*80)
        print("识别结果详情:")
        print("="*80)
        
        for i, (bbox, text, confidence) in enumerate(results, 1):
            # 计算边界框信息
            top_left = bbox[0]
            bottom_right = bbox[2]
            x1, y1 = int(top_left[0]), int(top_left[1])
            x2, y2 = int(bottom_right[0]), int(bottom_right[1])
            
            print(f"{i:2d}. 文字: '{text}'")
            print(f"    置信度: {confidence:.3f}")
            print(f"    位置: ({x1}, {y1}) -> ({x2}, {y2})")
            print(f"    大小: {x2-x1} x {y2-y1}")
            print()
            
            # 只保留置信度较高的结果
            if confidence > 0.3:
                ocr_data.append({
                    '序号': i,
                    '识别文字': text,
                    '置信度': round(confidence, 3),
                    '左上角X': x1,
                    '左上角Y': y1,
                    '右下角X': x2,
                    '右下角Y': y2,
                    '宽度': x2 - x1,
                    '高度': y2 - y1,
                    '截图文件': screenshot_filename,
                    '识别时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                all_text.append(text)
        
        print("="*80)
        print("完整识别文字:")
        print("="*80)
        full_text = '\n'.join(all_text)
        print(full_text)
        print("="*80)
        
        # 保存到Excel
        if ocr_data:
            df = pd.DataFrame(ocr_data)
            excel_filename = f"EasyOCR识别结果_{timestamp}.xlsx"
            df.to_excel(excel_filename, index=False, engine='openpyxl')
            print(f"\n✅ OCR结果已保存到Excel: {excel_filename}")
            print(f"✅ 共识别到 {len(ocr_data)} 个有效文字区域")
        else:
            print("\n⚠ 未识别到有效文字")
        
        # 保存完整文字到文本文件
        if full_text.strip():
            text_filename = f"识别的完整文字_{timestamp}.txt"
            with open(text_filename, 'w', encoding='utf-8') as f:
                f.write(full_text)
            print(f"✅ 完整文字已保存到: {text_filename}")
        
        return ocr_data
        
    except Exception as e:
        print(f"❌ EasyOCR测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_region_ocr():
    """测试指定区域的OCR识别"""
    print("\n=== 指定区域OCR识别测试 ===")
    
    try:
        # 初始化EasyOCR
        print("正在初始化EasyOCR...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        print("请用鼠标选择要识别文字的区域")
        print("将鼠标移动到区域左上角，然后按回车...")
        input()
        
        start_x, start_y = pyautogui.position()
        print(f"左上角: ({start_x}, {start_y})")
        
        print("将鼠标移动到区域右下角，然后按回车...")
        input()
        
        end_x, end_y = pyautogui.position()
        print(f"右下角: ({end_x}, {end_y})")
        
        # 计算区域
        width = end_x - start_x
        height = end_y - start_y
        
        if width > 0 and height > 0:
            print(f"捕获区域: {width} x {height} 像素")
            
            # 截取指定区域
            region_image = pyautogui.screenshot(region=(start_x, start_y, width, height))
            
            # 保存区域截图
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            region_filename = f"区域截图_{timestamp}.png"
            region_image.save(region_filename)
            
            # OCR识别
            print("正在识别区域内的文字...")
            image_array = np.array(region_image)
            results = reader.readtext(image_array)
            
            print(f"\n区域内识别到 {len(results)} 个文字:")
            print("-" * 50)
            
            region_texts = []
            for bbox, text, confidence in results:
                if confidence > 0.3:
                    print(f"文字: '{text}' (置信度: {confidence:.3f})")
                    region_texts.append(text)
            
            print("-" * 50)
            
            # 保存结果
            if region_texts:
                full_region_text = '\n'.join(region_texts)
                
                # 保存到Excel
                data = [{
                    '区域位置': f"({start_x}, {start_y}) -> ({end_x}, {end_y})",
                    '区域大小': f"{width} x {height}",
                    '识别文字': full_region_text,
                    '文字数量': len(region_texts),
                    '截图文件': region_filename,
                    '识别时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }]
                
                df = pd.DataFrame(data)
                excel_filename = f"区域OCR结果_{timestamp}.xlsx"
                df.to_excel(excel_filename, index=False, engine='openpyxl')
                
                print(f"✅ 区域OCR结果已保存: {excel_filename}")
            else:
                print("⚠ 区域内未识别到文字")
        else:
            print("❌ 无效的区域选择")
            
    except Exception as e:
        print(f"❌ 区域OCR测试失败: {e}")

if __name__ == "__main__":
    print("🔍 EasyOCR 屏幕文字识别测试工具")
    print("\n选择测试模式:")
    print("1. 全屏文字识别")
    print("2. 指定区域文字识别")
    
    try:
        choice = input("\n请选择 (1/2): ").strip()
        
        if choice == "1":
            test_easyocr_screen()
        elif choice == "2":
            test_region_ocr()
        else:
            print("默认运行全屏识别")
            test_easyocr_screen()
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
