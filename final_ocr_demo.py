import pyautogui
import pandas as pd
import time
import os
from datetime import datetime

def check_ocr_availability():
    """检查可用的OCR引擎"""
    ocr_engines = []
    
    # 检查EasyOCR
    try:
        import easyocr
        ocr_engines.append("EasyOCR")
    except ImportError:
        pass
    
    # 检查Pytesseract
    try:
        import pytesseract
        # 检查Tesseract可执行文件
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                ocr_engines.append("Pytesseract")
                break
    except ImportError:
        pass
    
    return ocr_engines

def perform_ocr_easyocr(image):
    """使用EasyOCR进行文字识别"""
    try:
        import easyocr
        import numpy as np
        
        # 初始化读取器
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 使用CPU模式
        
        # 转换图像格式
        image_array = np.array(image)
        
        # 进行OCR
        results = reader.readtext(image_array)
        
        # 提取文字和位置信息
        ocr_data = []
        full_text = []
        
        for (bbox, text, confidence) in results:
            if confidence > 0.3:  # 置信度阈值
                ocr_data.append({
                    '文字': text,
                    '置信度': round(confidence, 3),
                    '位置': str(bbox)
                })
                full_text.append(text)
        
        return '\n'.join(full_text), ocr_data
        
    except Exception as e:
        print(f"EasyOCR识别出错: {e}")
        return "", []

def perform_ocr_pytesseract(image):
    """使用Pytesseract进行文字识别"""
    try:
        import pytesseract
        
        # 进行OCR
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        
        # 获取详细信息
        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        # 提取有效文字
        ocr_data = []
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 30:  # 置信度阈值
                text_item = data['text'][i].strip()
                if text_item:
                    ocr_data.append({
                        '文字': text_item,
                        '置信度': data['conf'][i],
                        '位置': f"({data['left'][i]}, {data['top'][i]}, {data['width'][i]}, {data['height'][i]})"
                    })
        
        return text.strip(), ocr_data
        
    except Exception as e:
        print(f"Pytesseract识别出错: {e}")
        return "", []

def main_ocr_demo():
    """主OCR演示程序"""
    print("=== PyAutoGUI + OCR 完整演示 ===")
    
    # 检查OCR引擎
    available_engines = check_ocr_availability()
    
    if not available_engines:
        print("⚠ 没有可用的OCR引擎")
        print("请安装以下任一OCR引擎:")
        print("1. EasyOCR: pip install easyocr")
        print("2. Tesseract: 从 https://github.com/UB-Mannheim/tesseract/wiki 下载")
        
        # 仍然可以进行基础截图
        print("\\n继续进行基础截图功能演示...")
        basic_screenshot_demo()
        return
    
    print(f"✓ 可用的OCR引擎: {', '.join(available_engines)}")
    
    # 选择OCR引擎
    if len(available_engines) > 1:
        print("\\n选择OCR引擎:")
        for i, engine in enumerate(available_engines, 1):
            print(f"{i}. {engine}")
        
        try:
            choice = int(input("请选择 (输入数字): ")) - 1
            selected_engine = available_engines[choice]
        except:
            selected_engine = available_engines[0]
    else:
        selected_engine = available_engines[0]
    
    print(f"使用OCR引擎: {selected_engine}")
    
    # 开始OCR演示
    print("\\n准备进行OCR识别...")
    print("请确保屏幕上有要识别的文字内容")
    print("5秒后开始截图...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截图
    print("正在截图...")
    screenshot = pyautogui.screenshot()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"ocr_screenshot_{timestamp}.png"
    screenshot.save(screenshot_filename)
    
    # 进行OCR
    print(f"正在使用 {selected_engine} 识别文字...")
    
    if selected_engine == "EasyOCR":
        full_text, ocr_details = perform_ocr_easyocr(screenshot)
    else:  # Pytesseract
        full_text, ocr_details = perform_ocr_pytesseract(screenshot)
    
    # 显示结果
    print("\\n" + "="*60)
    print("识别到的文字:")
    print("="*60)
    print(full_text)
    print("="*60)
    
    # 保存到Excel
    if ocr_details:
        # 详细OCR数据
        df_details = pd.DataFrame(ocr_details)
        df_details['截图文件'] = screenshot_filename
        df_details['识别时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df_details['OCR引擎'] = selected_engine
        
        excel_filename = f"OCR识别结果_{timestamp}.xlsx"
        df_details.to_excel(excel_filename, index=False, engine='openpyxl')
        
        print(f"\\n✓ OCR结果已保存到Excel: {excel_filename}")
        print(f"✓ 共识别到 {len(ocr_details)} 个文字块")
    else:
        print("\\n⚠ 未识别到有效文字")
    
    # 保存完整文字到文本文件
    if full_text:
        text_filename = f"识别文字_{timestamp}.txt"
        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(full_text)
        print(f"✓ 完整文字已保存到: {text_filename}")

def basic_screenshot_demo():
    """基础截图演示（无需OCR）"""
    print("\\n=== 基础截图演示 ===")
    
    print("3秒后截图...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    screenshot = pyautogui.screenshot()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"基础截图_{timestamp}.png"
    screenshot.save(filename)
    
    # 获取屏幕信息
    screen_width, screen_height = pyautogui.size()
    mouse_x, mouse_y = pyautogui.position()
    
    # 保存到Excel
    data = [{
        '截图时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        '屏幕分辨率': f"{screen_width}x{screen_height}",
        '鼠标位置': f"({mouse_x}, {mouse_y})",
        '截图文件': filename,
        '备注': '基础截图功能演示'
    }]
    
    df = pd.DataFrame(data)
    excel_filename = f"基础截图数据_{timestamp}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"✓ 截图已保存: {filename}")
    print(f"✓ 数据已保存到Excel: {excel_filename}")

if __name__ == "__main__":
    try:
        main_ocr_demo()
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
