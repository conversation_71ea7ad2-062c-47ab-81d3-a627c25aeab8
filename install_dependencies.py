import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def main():
    print("=== 安装 OCR 和 Excel 处理依赖包 ===")
    
    packages = [
        "pytesseract",  # OCR文字识别
        "pillow",       # 图像处理
        "pandas",       # 数据处理
        "openpyxl",     # Excel文件处理
    ]
    
    success_count = 0
    for package in packages:
        print(f"\n正在安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成！成功安装 {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("\n所有依赖包安装成功！")
        print("\n注意：还需要安装 Tesseract OCR 引擎：")
        print("1. 下载：https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. 安装到默认路径：C:\\Program Files\\Tesseract-OCR\\")
        print("3. 或者修改 form_ocr_to_excel.py 中的 tesseract_cmd 路径")
    else:
        print("\n部分包安装失败，请检查网络连接或权限")

if __name__ == "__main__":
    main()
