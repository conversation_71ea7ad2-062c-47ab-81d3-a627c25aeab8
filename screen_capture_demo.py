import pyautogui
import time

# 1. 截取整个屏幕
print("1. 截取整个屏幕...")
screenshot = pyautogui.screenshot()
screenshot.save('full_screen.png')
print("全屏截图已保存为 full_screen.png")

# 2. 截取屏幕的指定区域
print("\n2. 截取屏幕指定区域...")
# 参数: (left, top, width, height)
region_screenshot = pyautogui.screenshot(region=(100, 100, 800, 600))
region_screenshot.save('region_screen.png')
print("区域截图已保存为 region_screen.png")

# 3. 获取指定像素点的颜色
print("\n3. 获取像素颜色...")
x, y = 500, 500
pixel_color = pyautogui.pixel(x, y)
print(f"坐标 ({x}, {y}) 的像素颜色 (RGB): {pixel_color}")

# 4. 在屏幕上查找图像（需要有参考图片）
print("\n4. 图像识别功能...")
try:
    # 查找图像在屏幕上的位置
    # location = pyautogui.locateOnScreen('target_image.png')
    # if location:
    #     print(f"找到图像位置: {location}")
    #     center = pyautogui.center(location)
    #     print(f"图像中心点: {center}")
    # else:
    #     print("未找到指定图像")
    print("图像识别功能需要参考图片文件")
except Exception as e:
    print(f"图像识别出错: {e}")

# 5. 获取屏幕上所有匹配的图像位置
print("\n5. 查找所有匹配的图像...")
try:
    # locations = list(pyautogui.locateAllOnScreen('target_image.png'))
    # print(f"找到 {len(locations)} 个匹配的图像")
    print("需要参考图片文件来演示此功能")
except Exception as e:
    print(f"查找所有图像出错: {e}")

# 6. 颜色匹配
print("\n6. 颜色匹配...")
# 检查指定位置是否为特定颜色
def check_color_at_position(x, y, expected_color, tolerance=10):
    actual_color = pyautogui.pixel(x, y)
    # 简单的颜色匹配（可以设置容差）
    r_diff = abs(actual_color[0] - expected_color[0])
    g_diff = abs(actual_color[1] - expected_color[1])
    b_diff = abs(actual_color[2] - expected_color[2])
    
    if r_diff <= tolerance and g_diff <= tolerance and b_diff <= tolerance:
        return True
    return False

# 示例：检查屏幕中心是否为白色
center_x, center_y = pyautogui.size()[0] // 2, pyautogui.size()[1] // 2
white_color = (255, 255, 255)
is_white = check_color_at_position(center_x, center_y, white_color)
print(f"屏幕中心 ({center_x}, {center_y}) 是否为白色: {is_white}")

print("\n屏幕捕获演示完成！")
