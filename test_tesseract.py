import pyautogui
import pytesseract
import pandas as pd
import os
import time
from datetime import datetime

def find_tesseract():
    """查找Tesseract安装路径"""
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe",
        "tesseract"  # 如果在PATH中
    ]
    
    for path in possible_paths:
        try:
            if path == "tesseract":
                # 测试是否在PATH中
                pytesseract.pytesseract.tesseract_cmd = "tesseract"
                version = pytesseract.get_tesseract_version()
                print(f"✓ Tesseract在PATH中，版本: {version}")
                return True
            elif os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                version = pytesseract.get_tesseract_version()
                print(f"✓ 找到Tesseract: {path}")
                print(f"✓ 版本: {version}")
                return True
        except Exception as e:
            continue
    
    print("❌ 未找到可用的Tesseract安装")
    return False

def test_tesseract_screen():
    """测试Tesseract屏幕文字识别"""
    print("=== Tesseract OCR 屏幕识别测试 ===")
    
    if not find_tesseract():
        return
    
    try:
        print("\n请确保屏幕上有要识别的文字")
        print("5秒后开始截图和识别...")
        
        for i in range(5, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # 截图
        print("\n📸 正在截取屏幕...")
        screenshot = pyautogui.screenshot()
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        screenshot_filename = f"tesseract_test_{timestamp}.png"
        screenshot.save(screenshot_filename)
        
        # 记录开始时间
        start_time = time.time()
        
        # Tesseract OCR识别
        print("🔍 正在使用Tesseract识别文字...")
        
        # 基础文字识别
        text = pytesseract.image_to_string(screenshot, lang='chi_sim+eng')
        
        # 获取详细数据（包含位置和置信度）
        data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱ 处理时间: {processing_time:.2f} 秒")
        
        # 处理详细结果
        detailed_results = []
        valid_texts = []
        
        for i in range(len(data['text'])):
            confidence = int(data['conf'][i])
            text_item = data['text'][i].strip()
            
            if confidence > 30 and text_item:  # 置信度阈值
                valid_texts.append(text_item)
                detailed_results.append({
                    '文字': text_item,
                    '置信度': confidence,
                    '左边距': data['left'][i],
                    '顶边距': data['top'][i],
                    '宽度': data['width'][i],
                    '高度': data['height'][i],
                    '位置': f"({data['left'][i]}, {data['top'][i]})"
                })
        
        # 显示结果
        print(f"\n🎉 识别完成！")
        print(f"📊 统计信息:")
        print(f"   - 总文字长度: {len(text)} 字符")
        print(f"   - 有效文字块: {len(valid_texts)} 个")
        print(f"   - 处理时间: {processing_time:.2f} 秒")
        
        print(f"\n📝 识别的完整文字:")
        print("-" * 60)
        print(text)
        print("-" * 60)
        
        # 保存到Excel
        if detailed_results:
            # 详细结果
            df_details = pd.DataFrame(detailed_results)
            df_details['截图文件'] = screenshot_filename
            df_details['识别时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            excel_filename = f"Tesseract识别结果_{timestamp}.xlsx"
            
            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                # 详细结果
                df_details.to_excel(writer, sheet_name='详细结果', index=False)
                
                # 汇总信息
                summary_data = [{
                    'OCR引擎': 'Tesseract',
                    '处理时间(秒)': processing_time,
                    '识别文字数量': len(valid_texts),
                    '总文字长度': len(text),
                    '截图文件': screenshot_filename,
                    '测试时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }]
                
                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='汇总信息', index=False)
            
            print(f"✅ 结果已保存到Excel: {excel_filename}")
        
        # 保存完整文字
        if text.strip():
            text_filename = f"Tesseract识别文字_{timestamp}.txt"
            with open(text_filename, 'w', encoding='utf-8') as f:
                f.write(text)
            print(f"✅ 完整文字已保存到: {text_filename}")
        
        return detailed_results
        
    except Exception as e:
        print(f"❌ Tesseract测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    print("🔍 Tesseract OCR 测试工具")

    if PYTESSERACT_AVAILABLE:
        test_tesseract_screen()
    else:
        print("\n❌ Pytesseract不可用，请检查安装")
        print("安装命令: pip install pytesseract")
        print("还需要安装Tesseract OCR引擎")
