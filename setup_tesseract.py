import os
import urllib.request
import subprocess
import sys

def download_tesseract():
    """下载Tesseract OCR安装程序"""
    print("=== Tesseract OCR 自动安装程序 ===")
    
    # Tesseract Windows安装程序下载链接
    tesseract_url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
    installer_name = "tesseract-installer.exe"
    
    print(f"正在下载 Tesseract OCR 安装程序...")
    print(f"下载地址: {tesseract_url}")
    
    try:
        # 下载安装程序
        urllib.request.urlretrieve(tesseract_url, installer_name)
        print(f"✓ 下载完成: {installer_name}")
        return installer_name
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        return None

def install_tesseract(installer_path):
    """运行Tesseract安装程序"""
    if not os.path.exists(installer_path):
        print(f"安装程序不存在: {installer_path}")
        return False
    
    print("正在启动Tesseract安装程序...")
    print("请按照安装向导完成安装，建议安装到默认路径：")
    print("C:\\Program Files\\Tesseract-OCR\\")
    
    try:
        # 启动安装程序
        subprocess.run([installer_path], check=True)
        print("✓ 安装程序已启动")
        return True
    except Exception as e:
        print(f"✗ 启动安装程序失败: {e}")
        return False

def check_tesseract_installation():
    """检查Tesseract是否正确安装"""
    common_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe"
    ]
    
    print("\n检查Tesseract安装...")
    
    for path in common_paths:
        if os.path.exists(path):
            print(f"✓ 找到Tesseract: {path}")
            return path
    
    print("✗ 未找到Tesseract安装")
    return None

def create_test_ocr_script(tesseract_path=None):
    """创建OCR测试脚本"""
    script_content = f'''import pyautogui
import pytesseract
from PIL import Image
import pandas as pd
from datetime import datetime

# 配置Tesseract路径
'''
    
    if tesseract_path:
        script_content += f"pytesseract.pytesseract.tesseract_cmd = r'{tesseract_path}'\n"
    else:
        script_content += "# pytesseract.pytesseract.tesseract_cmd = r'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'\n"
    
    script_content += '''
def test_ocr():
    """测试OCR功能"""
    print("=== OCR功能测试 ===")
    
    try:
        # 测试Tesseract是否可用
        print("测试Tesseract版本...")
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract版本: {version}")
        
        # 截取屏幕并进行OCR
        print("\\n截取屏幕进行文字识别...")
        screenshot = pyautogui.screenshot()
        
        # 识别文字（中英文）
        text = pytesseract.image_to_string(screenshot, lang='eng')
        print("识别到的文字:")
        print("-" * 50)
        print(text[:500] + "..." if len(text) > 500 else text)
        print("-" * 50)
        
        # 保存结果到Excel
        data = {
            '识别时间': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            '识别文字长度': [len(text)],
            '文字内容': [text[:100] + "..." if len(text) > 100 else text],
            '完整文字': [text]
        }
        
        df = pd.DataFrame(data)
        excel_filename = f"ocr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        
        print(f"\\n✓ OCR结果已保存到: {excel_filename}")
        return True
        
    except Exception as e:
        print(f"✗ OCR测试失败: {e}")
        print("\\n可能的解决方案:")
        print("1. 确保Tesseract OCR已正确安装")
        print("2. 检查tesseract_cmd路径是否正确")
        print("3. 确保安装了中文语言包")
        return False

if __name__ == "__main__":
    test_ocr()
'''
    
    with open("test_ocr.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print(f"✓ OCR测试脚本已创建: test_ocr.py")

def main():
    print("=== Tesseract OCR 完整安装向导 ===")
    
    # 1. 检查是否已安装
    tesseract_path = check_tesseract_installation()
    
    if tesseract_path:
        print("Tesseract已安装，跳过下载步骤")
    else:
        # 2. 下载安装程序
        print("\\n步骤1: 下载Tesseract安装程序")
        installer = download_tesseract()
        
        if installer:
            # 3. 运行安装程序
            print("\\n步骤2: 安装Tesseract")
            install_tesseract(installer)
            
            print("\\n请完成安装后按回车继续...")
            input()
            
            # 4. 重新检查安装
            tesseract_path = check_tesseract_installation()
    
    # 5. 创建测试脚本
    print("\\n步骤3: 创建OCR测试脚本")
    create_test_ocr_script(tesseract_path)
    
    print("\\n=== 安装完成 ===")
    if tesseract_path:
        print("✓ Tesseract OCR 已就绪")
        print("✓ 可以运行 test_ocr.py 测试OCR功能")
    else:
        print("⚠ 请手动安装Tesseract OCR")
        print("下载地址: https://github.com/UB-Mannheim/tesseract/wiki")

if __name__ == "__main__":
    main()
