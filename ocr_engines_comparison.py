import pyautogui
import pandas as pd
import numpy as np
import time
import os
from datetime import datetime

# 检查EasyOCR
try:
    import easyocr
    EASYOCR_AVAILABLE = True
    print("✅ EasyOCR 可用")
except ImportError:
    EASYOCR_AVAILABLE = False
    print("❌ EasyOCR 不可用")

# 检查Tesseract
try:
    import pytesseract
    
    # 设置Tesseract路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe"
    ]
    
    tesseract_found = False
    for path in possible_paths:
        if os.path.exists(path):
            pytesseract.pytesseract.tesseract_cmd = path
            tesseract_found = True
            break
    
    if tesseract_found or True:  # 也尝试系统PATH
        try:
            version = pytesseract.get_tesseract_version()
            TESSERACT_AVAILABLE = True
            print(f"✅ Tesseract 可用，版本: {version}")
        except:
            TESSERACT_AVAILABLE = False
            print("❌ Tesseract 不可用")
    else:
        TESSERACT_AVAILABLE = False
        print("❌ Tesseract 不可用")
        
except ImportError:
    TESSERACT_AVAILABLE = False
    print("❌ pytesseract 未安装")

def run_easyocr(image):
    """运行EasyOCR识别"""
    try:
        start_time = time.time()
        
        # 初始化EasyOCR
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 转换图像
        image_array = np.array(image)
        
        # 进行OCR
        results = reader.readtext(image_array)
        
        end_time = time.time()
        
        # 处理结果
        texts = []
        for bbox, text, confidence in results:
            if confidence > 0.3:
                texts.append(text)
        
        return {
            'success': True,
            'processing_time': round(end_time - start_time, 2),
            'text_count': len(texts),
            'full_text': '\n'.join(texts),
            'raw_results': results
        }
        
    except Exception as e:
        return {'success': False, 'error': str(e)}

def run_tesseract(image):
    """运行Tesseract识别"""
    try:
        start_time = time.time()
        
        # 进行OCR
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        end_time = time.time()
        
        # 处理结果
        valid_texts = []
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 30:
                text_item = data['text'][i].strip()
                if text_item:
                    valid_texts.append(text_item)
        
        return {
            'success': True,
            'processing_time': round(end_time - start_time, 2),
            'text_count': len(valid_texts),
            'full_text': text.strip(),
            'raw_data': data
        }
        
    except Exception as e:
        return {'success': False, 'error': str(e)}

def compare_ocr_engines():
    """对比OCR引擎性能"""
    print("\n=== OCR引擎性能对比 ===")
    
    if not EASYOCR_AVAILABLE and not TESSERACT_AVAILABLE:
        print("❌ 没有可用的OCR引擎")
        return
    
    print("准备进行对比测试...")
    print("请确保屏幕上有清晰的中英文混合文字")
    print("5秒后开始截图...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截图
    print("\n📸 截取屏幕...")
    screenshot = pyautogui.screenshot()
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"对比测试_{timestamp}.png"
    screenshot.save(screenshot_filename)
    print(f"截图保存: {screenshot_filename}")
    
    results = {}
    
    # 测试EasyOCR
    if EASYOCR_AVAILABLE:
        print("\n🔍 测试 EasyOCR...")
        easyocr_result = run_easyocr(screenshot)
        if easyocr_result['success']:
            results['EasyOCR'] = easyocr_result
            print(f"✅ EasyOCR - 用时: {easyocr_result['processing_time']}秒, 文字: {easyocr_result['text_count']}个")
        else:
            print(f"❌ EasyOCR失败: {easyocr_result['error']}")
    
    # 测试Tesseract
    if TESSERACT_AVAILABLE:
        print("\n🔍 测试 Tesseract...")
        tesseract_result = run_tesseract(screenshot)
        if tesseract_result['success']:
            results['Tesseract'] = tesseract_result
            print(f"✅ Tesseract - 用时: {tesseract_result['processing_time']}秒, 文字: {tesseract_result['text_count']}个")
        else:
            print(f"❌ Tesseract失败: {tesseract_result['error']}")
    
    # 生成对比报告
    if len(results) >= 2:
        print("\n" + "="*80)
        print("📊 OCR引擎详细对比")
        print("="*80)
        
        comparison_data = []
        
        for engine_name, result in results.items():
            print(f"\n🔧 {engine_name}:")
            print(f"   ⏱ 处理时间: {result['processing_time']} 秒")
            print(f"   📝 识别文字数: {result['text_count']} 个")
            print(f"   📄 文字总长度: {len(result['full_text'])} 字符")
            
            comparison_data.append({
                'OCR引擎': engine_name,
                '处理时间(秒)': result['processing_time'],
                '识别文字数量': result['text_count'],
                '文字总长度': len(result['full_text']),
                '截图文件': screenshot_filename,
                '测试时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        
        # 性能分析
        print(f"\n🏆 性能分析:")
        easyocr_data = results.get('EasyOCR')
        tesseract_data = results.get('Tesseract')
        
        if easyocr_data and tesseract_data:
            # 速度对比
            if easyocr_data['processing_time'] < tesseract_data['processing_time']:
                speed_winner = "EasyOCR"
                speed_diff = tesseract_data['processing_time'] - easyocr_data['processing_time']
            else:
                speed_winner = "Tesseract"
                speed_diff = easyocr_data['processing_time'] - tesseract_data['processing_time']
            
            print(f"   ⚡ 速度优胜: {speed_winner} (快 {speed_diff:.2f} 秒)")
            
            # 文字数量对比
            if easyocr_data['text_count'] > tesseract_data['text_count']:
                text_winner = "EasyOCR"
                text_diff = easyocr_data['text_count'] - tesseract_data['text_count']
            else:
                text_winner = "Tesseract"
                text_diff = tesseract_data['text_count'] - easyocr_data['text_count']
            
            print(f"   📝 识别数量优胜: {text_winner} (多 {text_diff} 个)")
        
        # 保存对比结果
        df_comparison = pd.DataFrame(comparison_data)
        comparison_filename = f"OCR引擎对比_{timestamp}.xlsx"
        
        with pd.ExcelWriter(comparison_filename, engine='openpyxl') as writer:
            # 对比汇总
            df_comparison.to_excel(writer, sheet_name='性能对比', index=False)
            
            # 各引擎的完整文字
            for engine_name, result in results.items():
                text_data = [{
                    'OCR引擎': engine_name,
                    '完整识别文字': result['full_text'],
                    '处理时间': result['processing_time'],
                    '文字数量': result['text_count']
                }]
                df_text = pd.DataFrame(text_data)
                df_text.to_excel(writer, sheet_name=f'{engine_name}完整文字', index=False)
        
        print(f"\n📊 完整对比报告已保存: {comparison_filename}")
        
    elif len(results) == 1:
        engine_name = list(results.keys())[0]
        result = results[engine_name]
        print(f"\n✅ 只有 {engine_name} 可用")
        print(f"处理时间: {result['processing_time']} 秒")
        print(f"识别文字: {result['text_count']} 个")
    
    else:
        print("\n❌ 没有OCR引擎成功运行")

if __name__ == "__main__":
    compare_ocr_engines()
