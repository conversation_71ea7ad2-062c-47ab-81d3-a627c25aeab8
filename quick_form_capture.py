import pyautogui
import pandas as pd
import time
from datetime import datetime

def capture_form_regions_to_excel():
    """交互式表单区域捕获并保存到Excel"""
    print("=== 交互式表单捕获工具 ===")
    print("这个工具将帮你捕获表单的不同区域并保存到Excel")
    
    form_data = []
    region_count = 1
    
    while True:
        print(f"\n--- 捕获区域 {region_count} ---")
        
        # 获取区域名称
        field_name = input("请输入这个区域的字段名称（如：姓名、电话等）: ").strip()
        if not field_name:
            field_name = f"字段{region_count}"
        
        print(f"准备捕获 '{field_name}' 区域")
        print("请将鼠标移动到要捕获区域的左上角，然后按回车...")
        input()
        
        # 记录左上角位置
        start_x, start_y = pyautogui.position()
        print(f"左上角位置: ({start_x}, {start_y})")
        
        print("请将鼠标移动到要捕获区域的右下角，然后按回车...")
        input()
        
        # 记录右下角位置
        end_x, end_y = pyautogui.position()
        print(f"右下角位置: ({end_x}, {end_y})")
        
        # 计算区域大小
        width = end_x - start_x
        height = end_y - start_y
        
        if width > 0 and height > 0:
            # 截取指定区域
            region_image = pyautogui.screenshot(region=(start_x, start_y, width, height))
            
            # 保存区域截图
            region_filename = f"{field_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            region_image.save(region_filename)
            
            # 获取区域中心点的颜色作为特征
            center_x = start_x + width // 2
            center_y = start_y + height // 2
            center_color = pyautogui.pixel(center_x, center_y)
            
            # 手动输入字段值
            field_value = input(f"请输入 '{field_name}' 的值（或按回车跳过）: ").strip()
            
            # 记录数据
            region_data = {
                '字段名称': field_name,
                '字段值': field_value,
                '左上角X': start_x,
                '左上角Y': start_y,
                '右下角X': end_x,
                '右下角Y': end_y,
                '宽度': width,
                '高度': height,
                '中心点颜色RGB': f"({center_color[0]},{center_color[1]},{center_color[2]})",
                '截图文件': region_filename,
                '捕获时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            form_data.append(region_data)
            print(f"✓ 区域 '{field_name}' 已捕获")
            
            region_count += 1
        else:
            print("✗ 无效的区域选择，请重试")
            continue
        
        # 询问是否继续
        continue_choice = input("\\n是否继续捕获下一个字段？(y/n): ").lower()
        if continue_choice not in ['y', 'yes', '是']:
            break
    
    # 保存到Excel
    if form_data:
        df = pd.DataFrame(form_data)
        excel_filename = f"表单数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        
        print(f"\\n🎉 完成！")
        print(f"✓ 共捕获 {len(form_data)} 个字段")
        print(f"✓ 数据已保存到: {excel_filename}")
        
        # 显示汇总
        print("\\n📋 捕获汇总:")
        for i, data in enumerate(form_data, 1):
            print(f"{i}. {data['字段名称']}: {data['字段值']}")
    else:
        print("\\n没有捕获到任何数据")

def quick_screenshot_to_excel():
    """快速截图并保存基本信息到Excel"""
    print("=== 快速截图工具 ===")
    
    print("3秒后截取整个屏幕...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截图
    screenshot = pyautogui.screenshot()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"快速截图_{timestamp}.png"
    screenshot.save(screenshot_filename)
    
    # 获取屏幕信息
    screen_width, screen_height = pyautogui.size()
    mouse_x, mouse_y = pyautogui.position()
    
    # 创建Excel数据
    data = [{
        '截图时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        '屏幕分辨率': f"{screen_width}x{screen_height}",
        '鼠标位置': f"({mouse_x}, {mouse_y})",
        '截图文件': screenshot_filename,
        '文件大小': f"{os.path.getsize(screenshot_filename) / 1024:.1f} KB"
    }]
    
    # 保存到Excel
    df = pd.DataFrame(data)
    excel_filename = f"截图信息_{timestamp}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"✓ 截图已保存: {screenshot_filename}")
    print(f"✓ 信息已保存到Excel: {excel_filename}")

if __name__ == "__main__":
    print("=== PyAutoGUI 表单捕获工具 ===")
    print("选择功能:")
    print("1. 交互式表单区域捕获")
    print("2. 快速全屏截图")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            capture_form_regions_to_excel()
        elif choice == "2":
            import os
            quick_screenshot_to_excel()
        else:
            print("无效选择，运行交互式捕获")
            capture_form_regions_to_excel()
            
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
