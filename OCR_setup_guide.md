# PyAutoGUI + OCR 完整设置指南

## 🎯 功能概述
PyAutoGUI 可以捕获屏幕信息并写入Excel，具体包括：

### ✅ 已实现的功能
1. **屏幕截图** - 全屏或指定区域
2. **像素颜色获取** - 获取任意位置的RGB值
3. **图像位置识别** - 查找屏幕上的图片位置
4. **Excel数据保存** - 将捕获的信息保存到Excel文件

### 🔄 OCR文字识别功能
要实现文字识别，需要额外的OCR引擎：

## 📦 方案一：EasyOCR（推荐）
**优点**: 安装简单，无需额外配置，支持中英文
**缺点**: 文件较大（约1GB），首次使用需下载模型

```bash
pip install easyocr
```

## 📦 方案二：Tesseract OCR
**优点**: 轻量级，识别准确
**缺点**: 需要单独安装OCR引擎

### 安装步骤：
1. 下载Tesseract: https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径: `C:\Program Files\Tesseract-OCR\`
3. 安装Python包: `pip install pytesseract`

## 🚀 使用示例

### 基础屏幕捕获（无需OCR）
```python
import pyautogui
import pandas as pd

# 截图并保存信息
screenshot = pyautogui.screenshot()
screenshot.save('screen.png')

data = {'截图时间': [datetime.now()], '文件': ['screen.png']}
pd.DataFrame(data).to_excel('screen_data.xlsx')
```

### 完整OCR识别（需要OCR引擎）
```python
import pyautogui
import easyocr  # 或 pytesseract
import pandas as pd

# 截图
screenshot = pyautogui.screenshot()

# OCR识别
reader = easyocr.Reader(['ch_sim', 'en'])
results = reader.readtext(screenshot)

# 提取文字并保存到Excel
text_data = []
for (bbox, text, confidence) in results:
    text_data.append({'文字': text, '置信度': confidence})

pd.DataFrame(text_data).to_excel('ocr_results.xlsx')
```

## 📁 当前可用的工具

1. **quick_form_capture.py** - 交互式表单区域捕获（无需OCR）
2. **simple_form_reader.py** - 基础屏幕信息捕获
3. **form_to_excel_complete.py** - 完整OCR方案（需要OCR引擎）

## 🔧 下一步操作

### 立即可用：
运行 `python quick_form_capture.py` 进行交互式表单捕获

### 完整OCR功能：
1. 等待EasyOCR安装完成（正在后台安装中）
2. 或手动安装Tesseract OCR
3. 运行 `python form_to_excel_complete.py` 进行文字识别

## 💡 实际应用场景

1. **表单数据提取** - 从网页表单或应用程序中提取数据
2. **发票/收据识别** - 识别票据上的文字信息
3. **屏幕监控** - 定期截图并分析内容变化
4. **自动化测试** - 验证界面显示的文字内容
5. **数据录入** - 将纸质文档转换为Excel数据
