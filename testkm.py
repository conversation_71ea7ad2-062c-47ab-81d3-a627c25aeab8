import pyautogui
import time
# 获取屏幕尺寸
screen_width, screen_height = pyautogui.size()
print(f"屏幕分辨率: {screen_width}x{screen_height}")
# 移动鼠标到中心并点击
pyautogui.moveTo(screen_width/2, screen_height/2, duration=1)  # 1秒移动到中心
pyautogui.click()  # 左键单击
# 右键点击指定位置
pyautogui.rightClick(x=100, y=200)
# 拖动操作
pyautogui.moveTo(300, 300, duration=0.5)
pyautogui.dragTo(500, 500, duration=1, button='left')  # 左键拖动
# 滚轮滚动
pyautogui.scroll(10)  # 向上滚动10个单位