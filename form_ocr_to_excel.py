import pyautogui
import pytesseract
from PIL import Image
import pandas as pd
import time
import re

# 配置 Tesseract 路径（Windows 用户需要安装 Tesseract OCR）
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def capture_screen_region(x, y, width, height):
    """截取屏幕指定区域"""
    screenshot = pyautogui.screenshot(region=(x, y, width, height))
    return screenshot

def extract_text_from_image(image):
    """从图像中提取文字"""
    try:
        # 使用 Tesseract OCR 识别文字
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')  # 支持中英文
        return text.strip()
    except Exception as e:
        print(f"OCR识别出错: {e}")
        return ""

def parse_form_data(text):
    """解析表单文字，提取结构化数据"""
    lines = text.split('\n')
    form_data = {}
    
    for line in lines:
        line = line.strip()
        if ':' in line or '：' in line:
            # 分割键值对
            if ':' in line:
                key, value = line.split(':', 1)
            else:
                key, value = line.split('：', 1)
            
            key = key.strip()
            value = value.strip()
            
            if key and value:
                form_data[key] = value
    
    return form_data

def save_to_excel(data_list, filename='form_data.xlsx'):
    """将数据保存到Excel文件"""
    if not data_list:
        print("没有数据可保存")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(data_list)
    
    # 保存到Excel
    df.to_excel(filename, index=False, engine='openpyxl')
    print(f"数据已保存到 {filename}")

def capture_form_and_extract():
    """主函数：捕获表单并提取数据"""
    print("=== PyAutoGUI + OCR 表单识别演示 ===")
    
    # 等待用户准备
    print("请确保表单在屏幕上可见，5秒后开始截图...")
    time.sleep(5)
    
    # 1. 截取整个屏幕
    print("正在截取屏幕...")
    screenshot = pyautogui.screenshot()
    screenshot.save('current_screen.png')
    
    # 2. 提取文字
    print("正在识别文字...")
    extracted_text = extract_text_from_image(screenshot)
    print("识别到的文字:")
    print("-" * 50)
    print(extracted_text)
    print("-" * 50)
    
    # 3. 解析表单数据
    form_data = parse_form_data(extracted_text)
    print("\n解析的表单数据:")
    for key, value in form_data.items():
        print(f"{key}: {value}")
    
    # 4. 保存到Excel
    if form_data:
        save_to_excel([form_data])
    
    return form_data

def capture_specific_region_demo():
    """演示捕获特定区域的表单"""
    print("\n=== 指定区域捕获演示 ===")
    print("请将鼠标移动到要捕获区域的左上角，5秒后记录位置...")
    time.sleep(5)
    
    # 获取当前鼠标位置作为起始点
    start_x, start_y = pyautogui.position()
    print(f"起始位置: ({start_x}, {start_y})")
    
    print("请将鼠标移动到要捕获区域的右下角，5秒后记录位置...")
    time.sleep(5)
    
    end_x, end_y = pyautogui.position()
    print(f"结束位置: ({end_x}, {end_y})")
    
    # 计算区域
    width = end_x - start_x
    height = end_y - start_y
    
    if width > 0 and height > 0:
        print(f"捕获区域: ({start_x}, {start_y}, {width}, {height})")
        
        # 截取指定区域
        region_image = capture_screen_region(start_x, start_y, width, height)
        region_image.save('form_region.png')
        
        # 识别文字
        text = extract_text_from_image(region_image)
        print("区域内识别的文字:")
        print(text)
        
        # 解析并保存
        form_data = parse_form_data(text)
        if form_data:
            save_to_excel([form_data], 'region_form_data.xlsx')
    else:
        print("无效的区域选择")

if __name__ == "__main__":
    # 选择运行模式
    print("选择运行模式:")
    print("1. 全屏捕获")
    print("2. 指定区域捕获")
    
    try:
        choice = input("请输入选择 (1 或 2): ")
        
        if choice == "1":
            capture_form_and_extract()
        elif choice == "2":
            capture_specific_region_demo()
        else:
            print("无效选择，默认运行全屏捕获")
            capture_form_and_extract()
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
