import pyautogui
import pytesseract
from PIL import Image
import pandas as pd
from datetime import datetime

# 配置Tesseract路径
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def test_ocr():
    """测试OCR功能"""
    print("=== OCR功能测试 ===")
    
    try:
        # 测试Tesseract是否可用
        print("测试Tesseract版本...")
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract版本: {version}")
        
        # 截取屏幕并进行OCR
        print("\n截取屏幕进行文字识别...")
        screenshot = pyautogui.screenshot()
        
        # 识别文字（中英文）
        text = pytesseract.image_to_string(screenshot, lang='eng')
        print("识别到的文字:")
        print("-" * 50)
        print(text[:500] + "..." if len(text) > 500 else text)
        print("-" * 50)
        
        # 保存结果到Excel
        data = {
            '识别时间': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            '识别文字长度': [len(text)],
            '文字内容': [text[:100] + "..." if len(text) > 100 else text],
            '完整文字': [text]
        }
        
        df = pd.DataFrame(data)
        excel_filename = f"ocr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        
        print(f"\n✓ OCR结果已保存到: {excel_filename}")
        return True
        
    except Exception as e:
        print(f"✗ OCR测试失败: {e}")
        print("\n可能的解决方案:")
        print("1. 确保Tesseract OCR已正确安装")
        print("2. 检查tesseract_cmd路径是否正确")
        print("3. 确保安装了中文语言包")
        return False

if __name__ == "__main__":
    test_ocr()
