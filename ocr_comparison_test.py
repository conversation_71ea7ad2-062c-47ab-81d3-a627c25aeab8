import pyautogui
import pandas as pd
import numpy as np
import time
import os
from datetime import datetime

# 导入OCR库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
    
    # 尝试常见的Tesseract路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Tesseract-OCR\tesseract.exe"
    ]
    
    tesseract_path = None
    for path in possible_paths:
        if os.path.exists(path):
            pytesseract.pytesseract.tesseract_cmd = path
            tesseract_path = path
            break
    
    if tesseract_path:
        print(f"✓ 找到Tesseract: {tesseract_path}")
    else:
        print("⚠ 未找到Tesseract可执行文件，请检查安装路径")
        
except ImportError:
    PYTESSERACT_AVAILABLE = False

def test_easyocr(image):
    """测试EasyOCR性能"""
    if not EASYOCR_AVAILABLE:
        return None, "EasyOCR未安装"
    
    try:
        start_time = time.time()
        
        # 初始化读取器
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 转换图像
        image_array = np.array(image)
        
        # 进行OCR
        results = reader.readtext(image_array)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 提取文字
        texts = []
        detailed_results = []
        
        for bbox, text, confidence in results:
            if confidence > 0.3:  # 置信度阈值
                texts.append(text)
                detailed_results.append({
                    '文字': text,
                    '置信度': round(confidence, 3),
                    '位置': f"{bbox[0]} -> {bbox[2]}"
                })
        
        return {
            'engine': 'EasyOCR',
            'processing_time': round(processing_time, 2),
            'text_count': len(texts),
            'full_text': '\n'.join(texts),
            'detailed_results': detailed_results,
            'status': 'success'
        }, None
        
    except Exception as e:
        return None, f"EasyOCR错误: {str(e)}"

def test_pytesseract(image):
    """测试Pytesseract性能"""
    if not PYTESSERACT_AVAILABLE:
        return None, "Pytesseract未安装"
    
    try:
        start_time = time.time()
        
        # 进行OCR识别
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        
        # 获取详细数据
        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 提取有效文字
        detailed_results = []
        valid_texts = []
        
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 30:  # 置信度阈值
                text_item = data['text'][i].strip()
                if text_item:
                    valid_texts.append(text_item)
                    detailed_results.append({
                        '文字': text_item,
                        '置信度': data['conf'][i],
                        '位置': f"({data['left'][i]}, {data['top'][i]}, {data['width'][i]}, {data['height'][i]})"
                    })
        
        return {
            'engine': 'Pytesseract',
            'processing_time': round(processing_time, 2),
            'text_count': len(valid_texts),
            'full_text': text.strip(),
            'detailed_results': detailed_results,
            'status': 'success'
        }, None
        
    except Exception as e:
        return None, f"Pytesseract错误: {str(e)}"

def compare_ocr_engines():
    """对比两个OCR引擎的性能"""
    print("=== OCR引擎性能对比测试 ===")
    
    # 检查可用的引擎
    available_engines = []
    if EASYOCR_AVAILABLE:
        available_engines.append("EasyOCR")
    if PYTESSERACT_AVAILABLE:
        available_engines.append("Pytesseract")
    
    if not available_engines:
        print("❌ 没有可用的OCR引擎")
        return
    
    print(f"✅ 可用的OCR引擎: {', '.join(available_engines)}")
    
    # 准备截图
    print("\n请确保屏幕上有清晰的文字内容")
    print("建议打开包含中英文混合文字的页面")
    print("5秒后开始截图...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    # 截取屏幕
    print("\n📸 正在截取屏幕...")
    screenshot = pyautogui.screenshot()
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    screenshot_filename = f"对比测试截图_{timestamp}.png"
    screenshot.save(screenshot_filename)
    print(f"截图已保存: {screenshot_filename}")
    
    # 测试结果存储
    comparison_results = []
    
    # 测试EasyOCR
    if EASYOCR_AVAILABLE:
        print("\n🔍 测试 EasyOCR...")
        easyocr_result, easyocr_error = test_easyocr(screenshot)
        if easyocr_result:
            comparison_results.append(easyocr_result)
            print(f"✅ EasyOCR完成 - 用时: {easyocr_result['processing_time']}秒, 识别文字: {easyocr_result['text_count']}个")
        else:
            print(f"❌ EasyOCR失败: {easyocr_error}")
    
    # 测试Pytesseract
    if PYTESSERACT_AVAILABLE:
        print("\n🔍 测试 Pytesseract...")
        pytesseract_result, pytesseract_error = test_pytesseract(screenshot)
        if pytesseract_result:
            comparison_results.append(pytesseract_result)
            print(f"✅ Pytesseract完成 - 用时: {pytesseract_result['processing_time']}秒, 识别文字: {pytesseract_result['text_count']}个")
        else:
            print(f"❌ Pytesseract失败: {pytesseract_error}")
    
    # 生成对比报告
    if len(comparison_results) >= 2:
        print("\n" + "="*80)
        print("📊 OCR引擎对比结果")
        print("="*80)
        
        for result in comparison_results:
            print(f"\n🔧 {result['engine']}:")
            print(f"   ⏱ 处理时间: {result['processing_time']} 秒")
            print(f"   📝 识别文字数: {result['text_count']} 个")
            print(f"   📄 文字长度: {len(result['full_text'])} 字符")
            print(f"   ✅ 状态: {result['status']}")
        
        # 保存对比结果到Excel
        summary_data = []
        for result in comparison_results:
            summary_data.append({
                'OCR引擎': result['engine'],
                '处理时间(秒)': result['processing_time'],
                '识别文字数量': result['text_count'],
                '文字总长度': len(result['full_text']),
                '截图文件': screenshot_filename,
                '测试时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        
        df_summary = pd.DataFrame(summary_data)
        summary_filename = f"OCR引擎对比_{timestamp}.xlsx"
        
        # 创建多个工作表
        with pd.ExcelWriter(summary_filename, engine='openpyxl') as writer:
            # 对比汇总
            df_summary.to_excel(writer, sheet_name='对比汇总', index=False)
            
            # 各引擎详细结果
            for result in comparison_results:
                if result['detailed_results']:
                    df_detail = pd.DataFrame(result['detailed_results'])
                    df_detail.to_excel(writer, sheet_name=f"{result['engine']}详细结果", index=False)
        
        print(f"\n📊 对比结果已保存到: {summary_filename}")
        
        # 显示推荐
        print("\n🏆 推荐建议:")
        if len(comparison_results) == 2:
            easyocr_res = next(r for r in comparison_results if r['engine'] == 'EasyOCR')
            tesseract_res = next(r for r in comparison_results if r['engine'] == 'Pytesseract')
            
            if easyocr_res['text_count'] > tesseract_res['text_count']:
                print("📈 EasyOCR 识别到更多文字")
            elif tesseract_res['text_count'] > easyocr_res['text_count']:
                print("📈 Pytesseract 识别到更多文字")
            else:
                print("📊 两个引擎识别文字数量相当")
            
            if easyocr_res['processing_time'] < tesseract_res['processing_time']:
                print("⚡ EasyOCR 处理速度更快")
            else:
                print("⚡ Pytesseract 处理速度更快")
    
    else:
        print(f"\n只有一个OCR引擎可用，无法进行对比")

if __name__ == "__main__":
    compare_ocr_engines()
